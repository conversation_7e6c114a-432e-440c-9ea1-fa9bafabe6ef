<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\Language;
use App\Models\ReadingSession;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

final class ReadingSessionsController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
    }

    public function index(Book $book): Collection
    {
        $sessions = ReadingSession::where('book_id', $book->id)
            ->where('user_id', Auth::id())
            ->with('language')
            ->orderBy('created_at', 'desc')
            ->get();

        return $sessions->map(function (ReadingSession $session) {
            return [
                'id' => $session->uuid,
                'bookId' => $session->book->uuid,
                'userId' => $session->user_id,
                'language' => $session->language ? [
                    'text' => $session->language->name,
                    'value' => $session->language->code,
                    'label' => $session->language->name,
                ] : null,
                'startDate' => $session->start_date?->toDateString(),
                'endDate' => $session->end_date?->toDateString(),
                'pagesRead' => $session->pages_read,
                'totalPages' => $session->total_pages,
                'createdAt' => $session->created_at->toISOString(),
                'updatedAt' => $session->updated_at->toISOString(),
            ];
        });
    }

    public function store(Request $request): array
    {
        $validated = $request->validate([
            'bookId' => 'required|string|exists:books,uuid',
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        $book = Book::where('uuid', $validated['bookId'])->firstOrFail();
        $language = null;

        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        $session = ReadingSession::create([
            'book_id' => $book->id,
            'user_id' => Auth::id(),
            'language_id' => $language?->id,
            'start_date' => $validated['startDate'] ?? null,
            'end_date' => $validated['endDate'] ?? null,
            'pages_read' => $validated['pagesRead'] ?? null,
            'total_pages' => $validated['totalPages'] ?? null,
        ]);

        $session->load('language');

        return [
            'success' => true,
            'session' => [
                'id' => $session->uuid,
                'bookId' => $session->book->uuid,
                'userId' => $session->user_id,
                'language' => $session->language ? [
                    'text' => $session->language->name,
                    'value' => $session->language->code,
                    'label' => $session->language->name,
                ] : null,
                'startDate' => $session->start_date?->toDateString(),
                'endDate' => $session->end_date?->toDateString(),
                'pagesRead' => $session->pages_read,
                'totalPages' => $session->total_pages,
                'createdAt' => $session->created_at->toISOString(),
                'updatedAt' => $session->updated_at->toISOString(),
            ],
        ];
    }

    public function update(string $sessionUuid, Request $request): array
    {
        $session = ReadingSession::where('uuid', $sessionUuid)->firstOrFail();

        // Ensure the session belongs to the authenticated user
        if ($session->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $validated = $request->validate([
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        $language = null;
        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        $session->update([
            'language_id' => $language?->id,
            'start_date' => $validated['startDate'] ?? null,
            'end_date' => $validated['endDate'] ?? null,
            'pages_read' => $validated['pagesRead'] ?? null,
            'total_pages' => $validated['totalPages'] ?? null,
        ]);

        $session->load('language');

        return [
            'success' => true,
            'session' => [
                'id' => $session->uuid,
                'bookId' => $session->book->uuid,
                'userId' => $session->user_id,
                'language' => $session->language ? [
                    'text' => $session->language->name,
                    'value' => $session->language->code,
                    'label' => $session->language->name,
                ] : null,
                'startDate' => $session->start_date?->toDateString(),
                'endDate' => $session->end_date?->toDateString(),
                'pagesRead' => $session->pages_read,
                'totalPages' => $session->total_pages,
                'createdAt' => $session->created_at->toISOString(),
                'updatedAt' => $session->updated_at->toISOString(),
            ],
        ];
    }

    public function destroy(string $sessionUuid): array
    {
        $session = ReadingSession::where('uuid', $sessionUuid)->firstOrFail();

        // Ensure the session belongs to the authenticated user
        if ($session->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $session->delete();

        return [
            'success' => true,
        ];
    }
}
