<?php declare(strict_types=1);

namespace App\Models;

use App\DataTransferObjects\BookDto;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;

/**
 * App\Models\Book
 *
 * @property int $id
 * @property string $uuid
 * @property int $views_count
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 */
final class Book extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;

    public function editions(): HasMany
    {
        return $this->hasMany(Edition::class)->with([
            'book',
            'identifiers',
            'publisher',
            'language',
        ]);
    }

    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(Author::class);
    }

    public function subjects(): BelongsToMany
    {
        return $this->belongsToMany(Subject::class)->where('show_in_menu', true);
    }

    public function likes(): HasMany
    {
        return $this->hasMany(BookLike::class);
    }

    public function readStatuses(): HasMany
    {
        return $this->hasMany(BookReadStatus::class);
    }

    public function readingSessions(): HasMany
    {
        return $this->hasMany(ReadingSession::class);
    }

    public function shares(): HasMany
    {
        return $this->hasMany(BookShare::class);
    }

    public function lists(): BelongsToMany
    {
        return $this->belongsToMany(BookList::class);
    }

    public function similarBooks(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'similar_books', 'book_id', 'similar_book_id')
            ->withPivot(['points', 'books_compare_limit']);
    }

    public function similarFreeBooks(): BelongsToMany
    {
        return $this->belongsToMany(Book::class, 'similar_free_books', 'book_id', 'similar_free_book_id')
            ->withPivot(['points', 'books_compare_limit']);
    }

    public function audiobooks(): HasMany
    {
        return $this->hasMany(Audiobook::class);
    }

    public function summaries(): HasMany
    {
        return $this->hasMany(BookSummary::class);
    }

    public function toSearchableArray(): array
    {
        if ($this->editions->count() === 0) {
            return [];
        }

        return $this->getMainEdition()->toSearchArray();
    }

    public function getScoutKey(): string
    {
        return $this->uuid;
    }

    public function getScoutKeyName(): string
    {
        return 'uuid';
    }

    protected function makeAllSearchableUsing($query)
    {
        return $query->with([
            'editions',
            'editions.identifiers',
            'authors',
            'subjects',
        ]);
    }

    public function audiobook(): ?Audiobook
    {
        return $this->audiobooks->sortByDesc('archive_downloads_count')->first();
    }

    public function getMainEdition(): ?Edition
    {
        return $this->editions->first();
    }

    public function isLiked(): int
    {
        /** @var $like BookLike */
        $like = $this->likes()->where('user_id', Auth::id())->first();

        if (empty($like)) {
            return 0;
        }

        if ($like->isPositive) {
            return 1;
        }

        return -1;
    }

    public function isDisliked(): bool
    {
        return $this->isLiked() === -1;
    }

    public function like(): bool
    {
        $this->removeLike();

        $like = new BookLike();
        $like->isPositive = true;
        $like->user_id = Auth::id();

        return (bool)$this->likes()->save($like);
    }

    public function dislike(): bool
    {
        $this->removeLike();

        $like = new BookLike();
        $like->isPositive = false;
        $like->user_id = Auth::id();

        return (bool)$this->likes()->save($like);
    }

    public function removeLike(): bool
    {
        return $this->likes()->where('user_id', Auth::id())->delete() > 0;
    }

    public function isInLists(): array
    {
        $bookLists = Auth::user()->bookLists;

        $lists = [];
        foreach ($bookLists as $bookList) {
            $lists[$bookList->uuid] = $this->lists->contains($bookList);
        }

        return $lists;
    }

    public function toDTO(): BookDto
    {
        return $this->getMainEdition()?->toDTO();
    }

    public static function getId(string $title, string $author): ?int
    {
        // Should compare lowercases of title and author for better results but that takes a long time
        // Maybe make a separate command for that
        $sql = '
            SELECT b.id
            FROM books b
            INNER JOIN editions ON editions.book_id = b.id
            INNER JOIN author_book ON author_book.book_id = b.id
            INNER JOIN authors ON authors.id = author_book.author_id
            WHERE editions.title = :editionsTitle
            AND authors.name = :authorsName;
        ';

        $books = DB::select($sql, [
            'editionsTitle' => $title,
            'authorsName' => $author,
        ]);

        return empty($books) ? null : $books[0]->id;
    }

    public static function getUuid(string $title, string $author): ?string
    {
        $sql = '
            SELECT b.uuid
            FROM books b
            INNER JOIN editions ON editions.book_id = b.id
            INNER JOIN author_book ON author_book.book_id = b.id
            INNER JOIN authors ON authors.id = author_book.author_id
            WHERE editions.title = :editionsTitle
            AND authors.name = :authorsName;
        ';

        $books = DB::select($sql, [
            'editionsTitle' => $title,
            'authorsName' => $author,
        ]);

        return empty($books) ? null : $books[0]->uuid;
    }
}
