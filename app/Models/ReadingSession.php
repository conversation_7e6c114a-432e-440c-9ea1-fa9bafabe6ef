<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * App\Models\ReadingSession
 *
 * @property int $id
 * @property string $uuid
 * @property int $book_id
 * @property int $user_id
 * @property int|null $language_id
 * @property Carbon|null $start_date
 * @property Carbon|null $end_date
 * @property int|null $pages_read
 * @property int|null $total_pages
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class ReadingSession extends Model
{
    protected $fillable = [
        'book_id',
        'user_id',
        'language_id',
        'start_date',
        'end_date',
        'pages_read',
        'total_pages',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'pages_read' => 'integer',
        'total_pages' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function toArray(): array
    {
        $array = parent::toArray();
        
        // Format dates as ISO strings for frontend
        if ($this->start_date) {
            $array['start_date'] = $this->start_date->toDateString();
        }
        if ($this->end_date) {
            $array['end_date'] = $this->end_date->toDateString();
        }

        // Include language data if available
        if ($this->relationLoaded('language') && $this->language) {
            $array['language'] = [
                'text' => $this->language->name,
                'value' => $this->language->code,
                'label' => $this->language->name,
            ];
        }

        return $array;
    }
}
