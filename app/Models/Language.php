<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\Language
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property string $code_3_b
 * @property string $code_3_t
 * @property int $show_in_menu
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 */
final class Language extends Model
{
    use HasFactory;
    use SoftDeletes;

    public const LANGUAGE_CODE_MAP = [
        'aa' => 'aar',
        'ab' => 'abk',
        'af' => 'afr',
        'ak' => 'aka',
        'sq' => 'alb',
        'am' => 'amh',
        'ar' => 'ara',
        'an' => 'arg',
        'hy' => 'arm',
        'as' => 'asm',
        'av' => 'ava',
        'ae' => 'ave',
        'ay' => 'aym',
        'az' => 'aze',
        'ba' => 'bak',
        'bm' => 'bam',
        'eu' => 'baq',
        'be' => 'bel',
        'bn' => 'ben',
        'bh' => 'bih',
        'bi' => 'bis',
        'bo' => 'tib',
        'bs' => 'bos',
        'br' => 'bre',
        'bg' => 'bul',
        'my' => 'bur',
        'ca' => 'cat',
        'cs' => 'cze',
        'ch' => 'cha',
        'ce' => 'che',
        'zh' => 'chi',
        'zh-CN' => 'chi',
        'zh-TW' => 'chi',
        'cu' => 'chu',
        'cv' => 'chv',
        'kw' => 'cor',
        'co' => 'cos',
        'cr' => 'cre',
        'cy' => 'wel',
        'da' => 'dan',
        'de' => 'ger',
        'dv' => 'div',
        'nl' => 'dut',
        'dz' => 'dzo',
        'el' => 'gre',
        'en' => 'eng',
        'En' => 'eng',
        'eo' => 'epo',
        'et' => 'est',
        'ee' => 'ewe',
        'fo' => 'fao',
        'fa' => 'per',
        'fj' => 'fij',
        'fi' => 'fin',
        'fr' => 'fre',
        'fy' => 'fry',
        'ff' => 'ful',
        'ka' => 'geo',
        'gd' => 'gla',
        'ga' => 'gle',
        'gl' => 'glg',
        'gv' => 'glv',
        'gn' => 'grn',
        'gu' => 'guj',
        'ht' => 'hat',
        'mfe' => 'hat',
        'ha' => 'hau',
        'he' => 'heb',
        'iw' => 'heb',
        'hz' => 'her',
        'hi' => 'hin',
        'ho' => 'hmo',
        'hr' => 'hrv',
        'hu' => 'hun',
        'ig' => 'ibo',
        'is' => 'ice',
        'io' => 'ido',
        'ii' => 'iii',
        'iu' => 'iku',
        'ie' => 'ile',
        'ia' => 'ina',
        'id' => 'ind',
        'in' => 'ind',
        'ik' => 'ipk',
        'it' => 'ita',
        'jv' => 'jav',
        'ja' => 'jpn',
        'kl' => 'kal',
        'kn' => 'kan',
        'ks' => 'kas',
        'kr' => 'kau',
        'kk' => 'kaz',
        'km' => 'khm',
        'ki' => 'kik',
        'rw' => 'kin',
        'ky' => 'kir',
        'kv' => 'kom',
        'kg' => 'kon',
        'ko' => 'kor',
        'kj' => 'kua',
        'ku' => 'kur',
        'lo' => 'lao',
        'la' => 'lat',
        'lv' => 'lav',
        'li' => 'lim',
        'ln' => 'lin',
        'lt' => 'lit',
        'lb' => 'ltz',
        'lu' => 'lub',
        'lg' => 'lug',
        'mk' => 'mac',
        'mh' => 'mah',
        'ml' => 'mal',
        'mi' => 'mao',
        'mr' => 'mar',
        'ms' => 'may',
        'mg' => 'mlg',
        'mt' => 'mlt',
        'mn' => 'mon',
        'na' => 'nau',
        'nv' => 'nav',
        'nr' => 'nbl',
        'nd' => 'nde',
        'ng' => 'ndo',
        'ne' => 'nep',
        'nn' => 'nno',
        'nb' => 'nob',
        'no' => 'nor',
        'ny' => 'nya',
        'oc' => 'oci',
        'oj' => 'oji',
        'or' => 'ori',
        'om' => 'orm',
        'os' => 'oss',
        'pa' => 'pan',
        'pi' => 'pli',
        'pl' => 'pol',
        'pt' => 'por',
        'ps' => 'pus',
        'qu' => 'que',
        'rm' => 'roh',
        'ro' => 'rum',
        'rn' => 'run',
        'ru' => 'rus',
        'sg' => 'sag',
        'sa' => 'san',
        'si' => 'sin',
        'sk' => 'slo',
        'sl' => 'slv',
        'se' => 'sme',
        'sm' => 'smo',
        'sn' => 'sna',
        'sd' => 'snd',
        'so' => 'som',
        'st' => 'sot',
        'es' => 'spa',
        'sc' => 'srd',
        'sr' => 'srp',
        'ss' => 'ssw',
        'su' => 'sun',
        'sw' => 'swa',
        'asa' => 'swa',
        'ebu' => 'swa',
        'sv' => 'swe',
        'ty' => 'tah',
        'ta' => 'tam',
        'tt' => 'tat',
        'te' => 'tel',
        'tg' => 'tgk',
        'tl' => 'tgl',
        'th' => 'tha',
        'ti' => 'tir',
        'to' => 'ton',
        'tn' => 'tsn',
        'ts' => 'tso',
        'tk' => 'tuk',
        'tr' => 'tur',
        'tw' => 'twi',
        'ug' => 'uig',
        'uk' => 'ukr',
        'ur' => 'urd',
        'uz' => 'uzb',
        've' => 'ven',
        'vi' => 'vie',
        'vo' => 'vol',
        'wa' => 'wln',
        'wo' => 'wol',
        'xh' => 'xho',
        'yi' => 'yid',
        'yo' => 'yor',
        'za' => 'zha',
        'zu' => 'zul',
        'yu' => 'f', // 'f' is the uncategorized language, 'yu' are all languages from Yugoslavia
    ];

    public function editions(): HasMany
    {
        return $this->hasMany(Edition::class);
    }

    public function audiobooks(): HasMany
    {
        return $this->hasMany(Audiobook::class);
    }
}
