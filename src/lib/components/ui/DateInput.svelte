<script lang="ts">
    import StringService from '$lib/services/StringService';

    export let title: string;
    export let value: string = '';
    export let required: boolean = false;
    export let showLabel: boolean = false;
    export let error: string | undefined = undefined;

    const id = StringService.generatePseudoUniqueString('date-input');
</script>

<div class="relative w-full">
    <label for={id} class:sr-only={!showLabel}>{title}</label>
    <input
        id={id}
        type="date"
        class="block w-full rounded-lg border bg-white p-2.5 text-sm text-gray-900 placeholder-gray-500 focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
        class:border-gray-300={error === undefined}
        class:border-red-700={error !== undefined}
        class:dark:border-gray-600={error === undefined}
        bind:value={value}
        placeholder={showLabel ? '' : title}
        {required}
    />
    {#if error !== undefined}
        <span class="text-sm text-red-700">{error}</span>
    {/if}
</div>
