<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import DateInput from '$lib/components/ui/DateInput.svelte';
    import Dropdown from '$lib/components/ui/Dropdown.svelte';
    import NumberInput from '$lib/components/ui/NumberInput.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingSession from '$lib/domain/ReadingSession';
    import {t} from '$lib/localization/Localization';

    export let book: Book;
    export let session: Partial<ReadingSession> = {};
    export let isEditing: boolean = false;
    export let isLoading: boolean = false;

    const dispatch = createEventDispatcher();

    // Form fields
    let selectedLanguage: Language | null = session.language || null;
    let startDate: string = session.startDate || '';
    let endDate: string = session.endDate || '';
    let pagesRead: string = session.pagesRead?.toString() || '';
    let totalPages: string = session.totalPages?.toString() || '';

    // Language options for dropdown
    $: languageOptions = book.languages.map(lang => ({
        value: lang.value,
        label: lang.text,
        icon: undefined
    }));

    // Auto-populate defaults
    $: if (!isEditing && !session.id) {
        // Set default language to first book language
        if (!selectedLanguage && book.languages.length > 0) {
            selectedLanguage = book.languages[0];
        }
        
        // Set default total pages from book metadata
        if (!totalPages && book.numberOfPages) {
            totalPages = book.numberOfPages.toString();
        }
    }

    function handleLanguageSelect(event: CustomEvent) {
        const option = event.detail;
        selectedLanguage = option ? book.languages.find(lang => lang.value === option.value) || null : null;
    }

    function handleSave() {
        const sessionData = {
            ...session,
            bookId: book.uuid,
            language: selectedLanguage,
            startDate: startDate || null,
            endDate: endDate || null,
            pagesRead: pagesRead ? parseInt(pagesRead) : null,
            totalPages: totalPages ? parseInt(totalPages) : null,
        };

        dispatch('save', sessionData);
    }

    function handleAddSession() {
        const sessionData = {
            bookId: book.uuid,
            language: selectedLanguage,
            startDate: startDate || null,
            endDate: endDate || null,
            pagesRead: pagesRead ? parseInt(pagesRead) : null,
            totalPages: totalPages ? parseInt(totalPages) : null,
        };

        dispatch('addSession', sessionData);
        
        // Reset form for next session
        selectedLanguage = book.languages.length > 0 ? book.languages[0] : null;
        startDate = '';
        endDate = '';
        pagesRead = '';
        totalPages = book.numberOfPages?.toString() || '';
    }

    function handleCancel() {
        dispatch('cancel');
    }
</script>

<div class="space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-800">
    <!-- Language Selection -->
    <div>
        <label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
            {$t('book.languages')}
        </label>
        <Dropdown
            options={languageOptions}
            selectedOption={selectedLanguage ? {
                value: selectedLanguage.value,
                label: selectedLanguage.text,
                icon: undefined
            } : null}
            placeholder="Select language"
            showClearOption={true}
            clearOptionLabel="No language"
            buttonClass="w-full justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            on:select={handleLanguageSelect}
            on:clear={() => selectedLanguage = null}
        />
    </div>

    <!-- Date Range -->
    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
        <div>
            <label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Start Date
            </label>
            <DateInput title="Start Date" bind:value={startDate} />
        </div>
        <div>
            <label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                End Date
            </label>
            <DateInput title="End Date" bind:value={endDate} />
        </div>
    </div>

    <!-- Pages -->
    <div class="grid grid-cols-2 gap-3">
        <div>
            <label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Pages Read
            </label>
            <NumberInput title="Pages Read" bind:value={pagesRead} min={0} />
        </div>
        <div>
            <label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Total Pages
            </label>
            <NumberInput title="Total Pages" bind:value={totalPages} min={1} />
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-2 pt-2">
        {#if isEditing}
            <Button
                title="Cancel"
                buttonClass="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700"
                on:click={handleCancel}
                disabled={isLoading}
            />
            <Button
                title="Save"
                buttonClass="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                on:click={handleSave}
                disabled={isLoading}
            />
        {:else}
            <Button
                title="Add Session"
                buttonClass="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                on:click={handleAddSession}
                disabled={isLoading}
            />
            <Button
                title="Save"
                buttonClass="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                on:click={handleSave}
                disabled={isLoading}
            />
        {/if}
    </div>
</div>
