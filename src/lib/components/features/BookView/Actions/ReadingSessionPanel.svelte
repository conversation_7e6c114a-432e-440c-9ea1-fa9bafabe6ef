<script lang="ts">
    import {onMount} from 'svelte';
    import {slide} from 'svelte/transition';
    import {quintOut} from 'svelte/easing';
    import ReadingSessionsApiClient from '$lib/api/ReadingSessionsApiClient';
    import ReadingSessionForm from './ReadingSessionForm.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import CalendarSvg from '$lib/components/svg/CalendarSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import type Book from '$lib/domain/Book';
    import type BookReadStatusType from '$lib/domain/BookReadStatusType';
    import type ReadingSession from '$lib/domain/ReadingSession';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let book: Book;
    export let readStatus: BookReadStatusType | null;

    let sessions: ReadingSession[] = [];
    let isLoading = false;
    let isEditMode = false;
    let editingSessionId: string | null = null;
    let showPanel = false;

    const readingSessionsApiClient = new ReadingSessionsApiClient();

    // Show panel when status is not "wantToRead" and not null
    $: showPanel = readStatus !== null && readStatus !== 'wantToRead';

    // Auto-populate dates based on reading status
    $: defaultStartDate = readStatus === 'currentlyReading' ? new Date().toISOString().split('T')[0] : '';
    $: defaultEndDate = readStatus === 'read' ? new Date().toISOString().split('T')[0] : '';

    onMount(async () => {
        if (showPanel) {
            await loadSessions();
        }
    });

    // Reload sessions when panel becomes visible
    $: if (showPanel && sessions.length === 0) {
        loadSessions();
    }

    async function loadSessions() {
        if (!showPanel) return;

        isLoading = true;
        try {
            const response = await readingSessionsApiClient.index(book.uuid);
            if (response.ok) {
                sessions = response.data;
            }
        } catch (error) {
            console.error('Failed to load reading sessions:', error);
            ToastNotificationsStore.push({
                text: $t('readingSessions.errors.loadFailed'),
                type: 'warning',
            });
        } finally {
            isLoading = false;
        }
    }

    async function handleSaveSession(event: CustomEvent) {
        const sessionData = event.detail;
        isLoading = true;

        try {
            let response;
            if (editingSessionId) {
                // Update existing session
                response = await readingSessionsApiClient.update(editingSessionId, sessionData);
            } else {
                // Create new session
                response = await readingSessionsApiClient.store(sessionData);
            }

            if (response.ok && response.data.success) {
                await loadSessions();
                isEditMode = false;
                editingSessionId = null;
                ToastNotificationsStore.push({
                    text: editingSessionId ? $t('readingSessions.success.updated') : $t('readingSessions.success.created'),
                    type: 'success',
                });
            } else {
                throw new Error('Failed to save session');
            }
        } catch (error) {
            console.error('Failed to save session:', error);
            ToastNotificationsStore.push({
                text: $t('readingSessions.errors.saveFailed'),
                type: 'warning',
            });
        } finally {
            isLoading = false;
        }
    }

    async function handleAddSession(event: CustomEvent) {
        const sessionData = event.detail;
        isLoading = true;

        try {
            const response = await readingSessionsApiClient.store(sessionData);
            if (response.ok && response.data.success) {
                await loadSessions();
                ToastNotificationsStore.push({
                    text: $t('readingSessions.success.created'),
                    type: 'success',
                });
            } else {
                throw new Error('Failed to add session');
            }
        } catch (error) {
            console.error('Failed to add session:', error);
            ToastNotificationsStore.push({
                text: $t('readingSessions.errors.saveFailed'),
                type: 'warning',
            });
        } finally {
            isLoading = false;
        }
    }

    function handleEditSession(session: ReadingSession) {
        editingSessionId = session.id;
        isEditMode = true;
    }

    function handleCancelEdit() {
        isEditMode = false;
        editingSessionId = null;
    }

    async function handleDeleteSession(sessionId: string) {
        if (!confirm($t('readingSessions.deleteConfirm'))) {
            return;
        }

        isLoading = true;
        try {
            const response = await readingSessionsApiClient.destroy(sessionId);
            if (response.ok && response.data.success) {
                await loadSessions();
                ToastNotificationsStore.push({
                    text: $t('readingSessions.success.deleted'),
                    type: 'success',
                });
            } else {
                throw new Error('Failed to delete session');
            }
        } catch (error) {
            console.error('Failed to delete session:', error);
            ToastNotificationsStore.push({
                text: $t('readingSessions.errors.deleteFailed'),
                type: 'warning',
            });
        } finally {
            isLoading = false;
        }
    }

    function formatDate(dateString: string | null): string {
        if (!dateString) return $t('readingSessions.notSet');
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function formatPages(pagesRead: number | null, totalPages: number | null): string {
        if (pagesRead === null && totalPages === null) return $t('readingSessions.notSet');
        if (pagesRead === null) return `0 ${$t('readingSessions.of')} ${totalPages}`;
        if (totalPages === null) return `${pagesRead} ${$t('readingSessions.pages')}`;
        return `${pagesRead} ${$t('readingSessions.of')} ${totalPages}`;
    }
</script>

{#if showPanel}
    <div
        class="mt-3"
        transition:slide={{ duration: 300, easing: quintOut }}
    >
        <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-600 dark:bg-gray-800">
            <!-- Header -->
            <div class="mb-4 flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {$t('readingSessions.title')}
                </h3>
                {#if !isEditMode && sessions.length > 0}
                    <button
                        type="button"
                        class="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                        on:click={() => isEditMode = true}
                        disabled={isLoading}
                    >
                        ✏️ {$t('readingSessions.edit')}
                    </button>
                {/if}
            </div>

            <!-- Loading State -->
            {#if isLoading}
                <div class="flex items-center justify-center py-4">
                    <div class="h-6 w-6 animate-spin rounded-full border-2 border-primary-600 border-t-transparent"></div>
                </div>
            {:else if isEditMode}
                <!-- Edit Mode -->
                <ReadingSessionForm
                    {book}
                    session={editingSessionId ? sessions.find(s => s.id === editingSessionId) || {} : {}}
                    {defaultStartDate}
                    {defaultEndDate}
                    isEditing={!!editingSessionId}
                    {isLoading}
                    on:save={handleSaveSession}
                    on:addSession={handleAddSession}
                    on:cancel={handleCancelEdit}
                />
            {:else if sessions.length === 0}
                <!-- No Sessions - Show Form -->
                <ReadingSessionForm
                    {book}
                    session={{}}
                    {defaultStartDate}
                    {defaultEndDate}
                    isEditing={false}
                    {isLoading}
                    on:save={handleSaveSession}
                    on:addSession={handleAddSession}
                />
            {:else}
                <!-- View Mode - Display Sessions -->
                <div class="space-y-4">
                    {#each sessions as session, index (session.id)}
                        <div class="rounded-md border border-gray-100 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-900">
                            <div class="flex items-start justify-between">
                                <div class="space-y-2">
                                    {#if session.language}
                                        <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                            <LanguageSvg svgClass="h-4 w-4" />
                                            <span>{$t('readingSessions.language')}: {session.language.text}</span>
                                        </div>
                                    {/if}

                                    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                        <CalendarSvg svgClass="h-4 w-4" />
                                        <span>{$t('readingSessions.start')}: {formatDate(session.startDate)}</span>
                                    </div>

                                    {#if session.endDate}
                                        <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                            <CalendarSvg svgClass="h-4 w-4" />
                                            <span>{$t('readingSessions.end')}: {formatDate(session.endDate)}</span>
                                        </div>
                                    {/if}

                                    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                        <BookOpenSvg svgClass="h-4 w-4" />
                                        <span>{$t('readingSessions.pages')}: {formatPages(session.pagesRead, session.totalPages)}</span>
                                    </div>
                                </div>

                                {#if index === 0}
                                    <button
                                        type="button"
                                        class="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
                                        on:click={() => handleEditSession(session)}
                                        disabled={isLoading}
                                    >
                                        ✏️ {$t('readingSessions.edit')}
                                    </button>
                                {/if}
                            </div>
                        </div>
                    {/each}
                </div>
            {/if}
        </div>
    </div>
{/if}
