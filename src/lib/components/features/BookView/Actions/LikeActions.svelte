<script lang="ts">
    import {goto} from '$app/navigation';
    import LikesApiClient from '$lib/api/LikesApiClient';
    import ThumbsDownOutlineSvg from '$lib/components/svg/ThumbsDownOutlineSvg.svelte';
    import ThumbsDownSvg from '$lib/components/svg/ThumbsDownSvg.svelte';
    import ThumbsUpOutlineSvg from '$lib/components/svg/ThumbsUpOutlineSvg.svelte';
    import ThumbsUpSvg from '$lib/components/svg/ThumbsUpSvg.svelte';
    import type Book from '$lib/domain/Book';
    import {t} from '$lib/localization/Localization';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    async function handleLikeAction(likeState: -1 | 0 | 1 = 1) {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);

            return;
        }

        const previousLikeState = book.isLiked;
        book.isLiked = likeState;

        let apiRoute;
        if (likeState === 1) {
            apiRoute = 'like';
        } else if (likeState === 0) {
            apiRoute = 'removeLike';
        } else {
            apiRoute = 'dislike';
        }

        const likesApiClient = new LikesApiClient();
        const response = await likesApiClient[apiRoute](book.uuid);

        if (!response.ok || !response.data.success) {
            book.isLiked = previousLikeState;
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }
</script>

<div class="flex rounded-xl overflow-hidden min-w-[80px]">
    <button
        type="button"
        class="flex items-center justify-center px-3 py-1.5 text-white bg-blue-700 hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none transition-colors flex-1"
        title={$t('book.like')}
        on:click={() => (book.isLiked === 1 ? handleLikeAction(0) : handleLikeAction(1))}
    >
        <svelte:component
            this={book.isLiked === 1 ? ThumbsUpSvg : ThumbsUpOutlineSvg}
            svgClass="w-5 h-5"
        />
    </button>
    <div class="w-px bg-blue-500 dark:bg-blue-400 self-stretch"></div>
    <button
        type="button"
        class="flex items-center justify-center px-3 py-1.5 text-white bg-blue-700 hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none transition-colors flex-1"
        title={$t('book.dislike')}
        on:click={() => (book.isLiked === -1 ? handleLikeAction(0) : handleLikeAction(-1))}
    >
        <svelte:component
            this={book.isLiked === -1 ? ThumbsDownSvg : ThumbsDownOutlineSvg}
            svgClass="w-5 h-5"
        />
    </button>
</div>
