<script lang="ts">
    import {onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import BookListsApiClient from '$lib/api/BookListsApiClient';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import FolderPlusSvg from '$lib/components/svg/FolderPlusSvg.svelte';
    import Button from "$lib/components/ui/Button.svelte";
    import Checkbox from '$lib/components/ui/Checkbox.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import type Book from '$lib/domain/Book';
    import type BookList from '$lib/domain/BookList';
    import {t} from '$lib/localization/Localization';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    let addToBookListModalOpen = false;
    let bookLists: BookList[] = [];
    const bookListsCheckboxes: boolean[] = [];

    async function openBookListsModal() {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);
        }

        addToBookListModalOpen = true;
    }

    async function loadBookLists() {
        if (!$AuthenticatedStore) {
            return;
        }

        const bookListsApiClient = new BookListsApiClient();
        const response = await bookListsApiClient.index();
        if (!response.ok) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });

            return;
        }

        bookLists = response.data.lists;
        bookLists.forEach((bookList) => {
            bookListsCheckboxes[bookList.id] = book.isInLists[bookList.id];
        });
    }

    async function changeBookListStatus(event) {
        book.isInLists[event.target.id] = event.target.checked;
        const booksApiClient = new BooksApiClient();

        if (event.target.checked) {
            const response = await booksApiClient.addToList(book.uuid, event.target.id);
            if (!response.ok || !response.data.success) {
                ToastNotificationsStore.push({
                    text: t.get('notifications.genericError', {}, {}),
                    type: 'warning',
                });
            }

            return;
        }

        const response = await booksApiClient.removeFromList(book.uuid, event.target.id);
        if (!response.ok || !response.data.success) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }

    onMount(() => {
        // todo: move this to the call that loads the page
        // currently it runs twice because BookActions is used twice in BookView
        loadBookLists();
    });
</script>

<div>
    <Button title={$t('book.addList')} callback={openBookListsModal} icon={FolderPlusSvg} />
    <Modal bind:open={addToBookListModalOpen}>
        <div class="mb-4 flex justify-between rounded-t sm:mb-5">
            <div class="text-lg text-gray-900 dark:text-white md:text-xl">
                <h3 class="font-semibold">{$t('book.addList')}</h3>
            </div>
        </div>
        <div class="space-y-4">
            {#each bookLists as bookList, index (index)}
                <Checkbox
                    id={bookList.id}
                    text={bookList.name}
                    checked={book.isInLists[bookList.id]}
                    onChange={changeBookListStatus}
                />
            {/each}
        </div>
    </Modal>
</div>
