<script lang="ts">
    import ChevronDown from 'lucide-svelte/icons/chevron-down';
    import {type ComponentType} from 'svelte';
    import {goto} from '$app/navigation';
    import BookReadStatusesApiClient from '$lib/api/BookReadStatusesApiClient';
    import BookClosedSvg from '$lib/components/svg/BookClosedSvg.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import StarSvg from '$lib/components/svg/StarSvg.svelte';
    import XCircle from '$lib/components/svg/XCircle.svelte';
    import XSvg from '$lib/components/svg/XSvg.svelte';
    import Dropdown from '$lib/components/ui/Dropdown.svelte';
    import type Book from '$lib/domain/Book';
    import type BookReadStatusType from '$lib/domain/BookReadStatusType';
    import {t} from '$lib/localization/Localization';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    type ReadStatusOption = {
        value: BookReadStatusType;
        label: string;
        icon: ComponentType;
        bgColor: string;
        textColor: string;
    };

    const readStatusOptions: ReadStatusOption[] = [
        {
            value: 'wantToRead',
            label: $t('bookStatuses.wantToRead'),
            icon: StarSvg,
            bgColor: 'bg-orange-500',
            textColor: 'text-white',
        },
        {
            value: 'currentlyReading',
            label: $t('bookStatuses.currentlyReading'),
            icon: BookOpenSvg,
            bgColor: 'bg-green-500',
            textColor: 'text-white',
        },
        {
            value: 'read',
            label: $t('bookStatuses.finished'),
            icon: BookClosedSvg,
            bgColor: 'bg-green-700',
            textColor: 'text-white',
        },
        {
            value: 'dropped',
            label: $t('bookStatuses.abandoned'),
            icon: XCircle,
            bgColor: 'bg-gray-500',
            textColor: 'text-white',
        },
    ];

    let selectedReadStatus = book.readStatus
        ? readStatusOptions.find((option) => option.value === book.readStatus)
        : null;

    async function handleReadStatusChange(option: ReadStatusOption | null) {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);
            return;
        }

        if (option === null) {
            book.readStatus = null;
            selectedReadStatus = null;

            const bookReadStatusesApiClient = new BookReadStatusesApiClient();
            const response = await bookReadStatusesApiClient.removeBookStatus(book.uuid);

            if (!response.ok || !response.data.success) {
                ToastNotificationsStore.push({
                    text: t.get('notifications.genericError', {}, {}),
                    type: 'warning',
                });
            }

            return;
        }

        if (option.value === book.readStatus) {
            return;
        }

        book.readStatus = option.value;
        selectedReadStatus = option;

        const bookReadStatusesApiClient = new BookReadStatusesApiClient();
        const response = await bookReadStatusesApiClient.setBookStatus(book.uuid, option.value);

        if (!response.ok || !response.data.success) {
            selectedReadStatus = book.readStatus
                ? readStatusOptions.find((opt) => opt.value === book.readStatus)
                : null;
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }
</script>

<Dropdown
    options={readStatusOptions}
    selectedOption={selectedReadStatus}
    placeholder={$t('bookStatuses.setReadingStatus')}
    showClearOption={true}
    clearOptionLabel="Clear status"
    clearOptionIcon={XSvg}
    buttonClass="inline-flex w-full items-center justify-between rounded-md px-4 py-2 text-sm font-medium shadow-sm focus:outline-none {selectedReadStatus
        ? `${selectedReadStatus.bgColor} ${selectedReadStatus.textColor} hover:opacity-90`
        : 'bg-primary-700 text-white hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700'}"
    on:select={(event) => handleReadStatusChange(event.detail)}
    on:clear={() => handleReadStatusChange(null)}
>
    <svelte:fragment slot="button" let:placeholder let:selectedOption>
        <div class="flex items-center space-x-2">
            {#if selectedOption}
                <svelte:component this={selectedOption.icon} svgClass="w-4 h-4 text-white" />
                <span>{selectedOption.label}</span>
            {:else}
                <span>{placeholder}</span>
            {/if}
        </div>
        <ChevronDown class="-mr-1 ml-2 h-5 w-5" />
    </svelte:fragment>
</Dropdown>
