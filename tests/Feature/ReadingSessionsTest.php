<?php declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Book;
use App\Models\Language;
use App\Models\ReadingSession;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class ReadingSessionsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Book $book;
    private Language $language;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->book = Book::factory()->create();
        $this->language = Language::factory()->create([
            'name' => 'English',
            'code' => 'eng',
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_create_reading_session(): void
    {
        $sessionData = [
            'bookId' => $this->book->uuid,
            'languageCode' => $this->language->code,
            'startDate' => '2025-01-01',
            'endDate' => '2025-01-31',
            'pagesRead' => 100,
            'totalPages' => 300,
        ];

        $response = $this->postJson('/api/reading-sessions', $sessionData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'session' => [
                    'bookId' => $this->book->uuid,
                    'userId' => $this->user->id,
                    'startDate' => '2025-01-01',
                    'endDate' => '2025-01-31',
                    'pagesRead' => 100,
                    'totalPages' => 300,
                ],
            ]);

        $this->assertDatabaseHas('reading_sessions', [
            'book_id' => $this->book->id,
            'user_id' => $this->user->id,
            'language_id' => $this->language->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
            'pages_read' => 100,
            'total_pages' => 300,
        ]);
    }

    public function test_can_list_reading_sessions_for_book(): void
    {
        $session = ReadingSession::create([
            'book_id' => $this->book->id,
            'user_id' => $this->user->id,
            'language_id' => $this->language->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31',
            'pages_read' => 100,
            'total_pages' => 300,
        ]);

        $response = $this->getJson("/api/reading-sessions/book/{$this->book->uuid}");

        $response->assertStatus(200)
            ->assertJsonCount(1)
            ->assertJsonFragment([
                'bookId' => $this->book->uuid,
                'userId' => $this->user->id,
                'startDate' => '2025-01-01',
                'endDate' => '2025-01-31',
                'pagesRead' => 100,
                'totalPages' => 300,
            ]);
    }

    public function test_can_update_reading_session(): void
    {
        $session = ReadingSession::create([
            'book_id' => $this->book->id,
            'user_id' => $this->user->id,
            'language_id' => $this->language->id,
            'start_date' => '2025-01-01',
            'pages_read' => 50,
            'total_pages' => 300,
        ]);

        $updateData = [
            'endDate' => '2025-01-31',
            'pagesRead' => 100,
        ];

        $response = $this->putJson("/api/reading-sessions/{$session->uuid}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'session' => [
                    'endDate' => '2025-01-31',
                    'pagesRead' => 100,
                ],
            ]);

        $this->assertDatabaseHas('reading_sessions', [
            'uuid' => $session->uuid,
            'end_date' => '2025-01-31',
            'pages_read' => 100,
        ]);
    }

    public function test_can_delete_reading_session(): void
    {
        $session = ReadingSession::create([
            'book_id' => $this->book->id,
            'user_id' => $this->user->id,
            'start_date' => '2025-01-01',
        ]);

        $response = $this->deleteJson("/api/reading-sessions/{$session->uuid}");

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $this->assertDatabaseMissing('reading_sessions', [
            'uuid' => $session->uuid,
        ]);
    }

    public function test_cannot_access_other_users_sessions(): void
    {
        $otherUser = User::factory()->create();
        $session = ReadingSession::create([
            'book_id' => $this->book->id,
            'user_id' => $otherUser->id,
            'start_date' => '2025-01-01',
        ]);

        // Try to update another user's session
        $response = $this->putJson("/api/reading-sessions/{$session->uuid}", [
            'pagesRead' => 100,
        ]);

        $response->assertStatus(403);

        // Try to delete another user's session
        $response = $this->deleteJson("/api/reading-sessions/{$session->uuid}");

        $response->assertStatus(403);
    }

    public function test_validates_required_fields(): void
    {
        $response = $this->postJson('/api/reading-sessions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['bookId']);
    }

    public function test_validates_date_order(): void
    {
        $response = $this->postJson('/api/reading-sessions', [
            'bookId' => $this->book->uuid,
            'startDate' => '2025-01-31',
            'endDate' => '2025-01-01', // End date before start date
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['endDate']);
    }
}
