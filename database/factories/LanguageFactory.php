<?php

namespace Database\Factories;

use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\Factory;

class LanguageFactory extends Factory
{
    protected $model = Language::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement(['English', 'Spanish', 'French', 'German', 'Italian']),
            'code' => $this->faker->randomElement(['eng', 'spa', 'fre', 'ger', 'ita']),
            'code_3_b' => $this->faker->randomElement(['eng', 'spa', 'fre', 'ger', 'ita']),
            'code_3_t' => $this->faker->randomElement(['eng', 'spa', 'fre', 'ger', 'ita']),
            'show_in_menu' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
